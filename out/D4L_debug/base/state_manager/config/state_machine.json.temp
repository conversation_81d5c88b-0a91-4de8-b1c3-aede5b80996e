{"Applications": [{"app": "pilot_roadmarking", "id": 1, "error_code": 10}, {"app": "pilot_vehicle", "id": 2, "error_code": 10}, {"app": "pilot_fusion", "id": 3, "error_code": 10}, {"app": "pilot_fusion", "id": 3, "error_code": 10}, {"app": "pilot_planning", "id": 4, "error_code": 10}, {"app": "apa_infer", "id": 14, "error_code": 11}, {"app": "apa_planning", "id": 16, "error_code": 11}, {"app": "apa_vtr", "id": 18, "error_code": 11}, {"app": "canout", "id": 23, "error_code": 36}, {"app": "caninput", "id": 24, "error_code": 35}, {"app": "uss_radar", "id": 27, "error_code": 32}, {"app": "road_trigger", "id": 28, "error_code": 34}, {"app": "app_avm_server", "id": 29, "error_code": 33}, {"app": "apa_calib", "id": 30, "error_code": 37}], "app_id_max": 128, "default_scenario": "PilotMode", "recovery_scenario": "<PERSON><PERSON><PERSON>", "scenario_switch_timeout": 1000, "scenarios": [{"scenario_name": "DefaultMode", "id": 0, "pause_apps": ["uss_radar", "caninput", "canout", "apa_infer", "apa_fusion", "apa_planning", "apa_calib", "app_avm_server"], "pre_resume_apps": [], "resume_apps": ["pilot_roadmarking", "pilot_vehicle", "pilot_fusion", "pilot_planning"], "start_apps": [], "stop_apps": []}, {"scenario_name": "PilotMode", "id": 1, "pause_apps": ["caninput", "apa_infer", "app_avm_server"], "pre_resume_apps": [], "resume_apps": ["pilot_vehicle"], "start_apps": [], "stop_apps": []}, {"scenario_name": "ApaMode", "id": 2, "pause_apps": ["pilot_vehicle"], "pre_resume_apps": ["app_avm_server"], "resume_apps": ["caninput", "apa_infer"], "start_apps": [], "stop_apps": []}, {"scenario_name": "GmingMode", "id": 3, "pause_apps": ["caninput", "apa_infer", "pilot_vehicle"], "pre_resume_apps": ["app_avm_server"], "resume_apps": [], "start_apps": [], "stop_apps": []}, {"scenario_name": "PilotCalibMode", "id": 4, "pause_apps": ["caninput", "apa_infer", "app_avm_server"], "pre_resume_apps": [], "resume_apps": ["pilot_vehicle"], "start_apps": [], "stop_apps": []}, {"scenario_name": "ApaCalibMode", "id": 5, "pause_apps": ["caninput", "pilot_vehicle"], "pre_resume_apps": ["app_avm_server"], "resume_apps": ["apa_infer"], "start_apps": [], "stop_apps": []}, {"scenario_name": "OtaMode", "id": 6, "pause_apps": ["caninput", "apa_infer", "pilot_vehicle", "app_avm_server"], "pre_resume_apps": [], "resume_apps": [], "start_apps": [], "stop_apps": []}, {"scenario_name": "ShutdownMode", "id": 7, "pause_apps": [], "pre_resume_apps": [], "resume_apps": [], "start_apps": [], "stop_apps": ["caninput", "apa_infer", "pilot_vehicle", "app_avm_server"]}]}